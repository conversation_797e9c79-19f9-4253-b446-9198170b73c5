{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-RelWithDebInfo-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": "."}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0], "name": "Project"}], "targets": []}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/Documents/augment-projects/new/ukilgiri_app/build/.cxx/RelWithDebInfo/49e54473/x86_64", "source": "C:/src/flutter/packages/flutter_tools/gradle/src/main/scripts"}, "version": {"major": 2, "minor": 3}}