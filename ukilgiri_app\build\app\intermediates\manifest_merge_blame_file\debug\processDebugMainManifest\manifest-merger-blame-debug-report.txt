1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.ukilgiri.app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:3:5-67
15-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:3:22-64
16    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
16-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:4:5-79
16-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:4:22-76
17    <uses-permission android:name="android.permission.CAMERA" />
17-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:5:5-65
17-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:5:22-62
18    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
18-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:6:5-80
18-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:6:22-77
19    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
19-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:7:5-81
19-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:7:22-78
20    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
20-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:8:5-76
20-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:8:22-73
21    <!--
22 Required to query activities that can process text, see:
23         https://developer.android.com/training/package-visibility?hl=en and
24         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
25
26         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
27    -->
28    <queries>
28-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:50:5-55:15
29        <intent>
29-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:51:9-54:18
30            <action android:name="android.intent.action.PROCESS_TEXT" />
30-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:52:13-72
30-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:52:21-70
31
32            <data android:mimeType="text/plain" />
32-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:53:13-50
32-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:53:19-48
33        </intent>
34    </queries>
35
36    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
36-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\f05cbc1323a19b3d7510097d6e3c9a69\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:9:5-98
36-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\f05cbc1323a19b3d7510097d6e3c9a69\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:9:22-95
37
38    <permission
38-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e7e3c83388001da39a5d44ececd8e9e\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
39        android:name="com.ukilgiri.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
39-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e7e3c83388001da39a5d44ececd8e9e\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
40        android:protectionLevel="signature" />
40-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e7e3c83388001da39a5d44ececd8e9e\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
41
42    <uses-permission android:name="com.ukilgiri.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
42-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e7e3c83388001da39a5d44ececd8e9e\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
42-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e7e3c83388001da39a5d44ececd8e9e\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
43
44    <application
45        android:name="android.app.Application"
46        android:allowBackup="true"
47        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e7e3c83388001da39a5d44ececd8e9e\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
48        android:debuggable="true"
49        android:extractNativeLibs="true"
50        android:icon="@mipmap/ic_launcher"
51        android:label="UkilGiri - উকিলগিরি"
52        android:requestLegacyExternalStorage="true"
53        android:supportsRtl="true"
54        android:usesCleartextTraffic="true" >
55        <activity
56            android:name="com.ukilgiri.app.MainActivity"
57            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
58            android:exported="true"
59            android:hardwareAccelerated="true"
60            android:launchMode="singleTop"
61            android:theme="@style/LaunchTheme"
62            android:windowSoftInputMode="adjustResize" >
63
64            <!--
65                 Specifies an Android theme to apply to this Activity as soon as
66                 the Android process has started. This theme is visible to the user
67                 while the Flutter UI initializes. After that, this theme continues
68                 to determine the Window background behind the Flutter UI.
69            -->
70            <meta-data
71                android:name="io.flutter.embedding.android.NormalTheme"
72                android:resource="@style/NormalTheme" />
73
74            <intent-filter>
75                <action android:name="android.intent.action.MAIN" />
76
77                <category android:name="android.intent.category.LAUNCHER" />
78            </intent-filter>
79        </activity>
80        <!--
81             Don't delete the meta-data below.
82             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
83        -->
84        <meta-data
85            android:name="flutterEmbedding"
86            android:value="2" />
87
88        <service
88-->[:firebase_auth] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
89            android:name="com.google.firebase.components.ComponentDiscoveryService"
89-->[:firebase_auth] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-89
90            android:directBootAware="true"
90-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\870c4a4d8ab6921fb09c369d676fe19c\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:32:13-43
91            android:exported="false" >
91-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:68:13-37
92            <meta-data
92-->[:firebase_auth] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
93                android:name="com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar"
93-->[:firebase_auth] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
94                android:value="com.google.firebase.components.ComponentRegistrar" />
94-->[:firebase_auth] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
95            <meta-data
95-->[:cloud_firestore] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
96                android:name="com.google.firebase.components:io.flutter.plugins.firebase.firestore.FlutterFirebaseFirestoreRegistrar"
96-->[:cloud_firestore] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-134
97                android:value="com.google.firebase.components.ComponentRegistrar" />
97-->[:cloud_firestore] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
98            <meta-data
98-->[:firebase_storage] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
99                android:name="com.google.firebase.components:io.flutter.plugins.firebase.storage.FlutterFirebaseAppRegistrar"
99-->[:firebase_storage] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-126
100                android:value="com.google.firebase.components.ComponentRegistrar" />
100-->[:firebase_storage] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
101            <meta-data
101-->[:firebase_core] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
102                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
102-->[:firebase_core] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
103                android:value="com.google.firebase.components.ComponentRegistrar" />
103-->[:firebase_core] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
104            <meta-data
104-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:69:13-71:85
105                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
105-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:70:17-109
106                android:value="com.google.firebase.components.ComponentRegistrar" />
106-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:71:17-82
107            <meta-data
107-->[com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\d83f2e8efb23072280f9a46b6129fa9a\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:17:13-19:85
108                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
108-->[com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\d83f2e8efb23072280f9a46b6129fa9a\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:18:17-122
109                android:value="com.google.firebase.components.ComponentRegistrar" />
109-->[com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\d83f2e8efb23072280f9a46b6129fa9a\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:19:17-82
110            <meta-data
110-->[com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\d83f2e8efb23072280f9a46b6129fa9a\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:20:13-22:85
111                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
111-->[com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\d83f2e8efb23072280f9a46b6129fa9a\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:21:17-111
112                android:value="com.google.firebase.components.ComponentRegistrar" />
112-->[com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\d83f2e8efb23072280f9a46b6129fa9a\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:22:17-82
113            <meta-data
113-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6528413aa607479f9d4a8f42981c48b7\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:30:13-32:85
114                android:name="com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar"
114-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6528413aa607479f9d4a8f42981c48b7\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:31:17-118
115                android:value="com.google.firebase.components.ComponentRegistrar" />
115-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6528413aa607479f9d4a8f42981c48b7\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:32:17-82
116            <meta-data
116-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6528413aa607479f9d4a8f42981c48b7\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:33:13-35:85
117                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
117-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6528413aa607479f9d4a8f42981c48b7\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:34:17-107
118                android:value="com.google.firebase.components.ComponentRegistrar" />
118-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6528413aa607479f9d4a8f42981c48b7\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:35:17-82
119            <meta-data
119-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\ef83a5b1da0a826e04415baff613d9b5\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:25:13-27:85
120                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
120-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\ef83a5b1da0a826e04415baff613d9b5\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:26:17-120
121                android:value="com.google.firebase.components.ComponentRegistrar" />
121-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\ef83a5b1da0a826e04415baff613d9b5\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:27:17-82
122            <meta-data
122-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\ef83a5b1da0a826e04415baff613d9b5\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:28:13-30:85
123                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
123-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\ef83a5b1da0a826e04415baff613d9b5\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:29:17-117
124                android:value="com.google.firebase.components.ComponentRegistrar" />
124-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\ef83a5b1da0a826e04415baff613d9b5\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:30:17-82
125            <meta-data
125-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\b32d05bfe0ef284c76253bd0a5e650d5\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:12:13-14:85
126                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
126-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\b32d05bfe0ef284c76253bd0a5e650d5\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:13:17-116
127                android:value="com.google.firebase.components.ComponentRegistrar" />
127-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\b32d05bfe0ef284c76253bd0a5e650d5\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:14:17-82
128            <meta-data
128-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\870c4a4d8ab6921fb09c369d676fe19c\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:35:13-37:85
129                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
129-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\870c4a4d8ab6921fb09c369d676fe19c\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:36:17-109
130                android:value="com.google.firebase.components.ComponentRegistrar" />
130-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\870c4a4d8ab6921fb09c369d676fe19c\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:37:17-82
131        </service>
132
133        <activity
133-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:29:9-46:20
134            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
134-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:30:13-80
135            android:excludeFromRecents="true"
135-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:31:13-46
136            android:exported="true"
136-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:32:13-36
137            android:launchMode="singleTask"
137-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:33:13-44
138            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
138-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:34:13-72
139            <intent-filter>
139-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:35:13-45:29
140                <action android:name="android.intent.action.VIEW" />
140-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:17-69
140-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:25-66
141
142                <category android:name="android.intent.category.DEFAULT" />
142-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:17-76
142-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:27-73
143                <category android:name="android.intent.category.BROWSABLE" />
143-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:17-78
143-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:27-75
144
145                <data
145-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:53:13-50
146                    android:host="firebase.auth"
147                    android:path="/"
148                    android:scheme="genericidp" />
149            </intent-filter>
150        </activity>
151        <activity
151-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:47:9-64:20
152            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
152-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:48:13-79
153            android:excludeFromRecents="true"
153-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:49:13-46
154            android:exported="true"
154-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:50:13-36
155            android:launchMode="singleTask"
155-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:51:13-44
156            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
156-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:52:13-72
157            <intent-filter>
157-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:53:13-63:29
158                <action android:name="android.intent.action.VIEW" />
158-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:17-69
158-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:25-66
159
160                <category android:name="android.intent.category.DEFAULT" />
160-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:17-76
160-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:27-73
161                <category android:name="android.intent.category.BROWSABLE" />
161-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:17-78
161-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:27-75
162
163                <data
163-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:53:13-50
164                    android:host="firebase.auth"
165                    android:path="/"
166                    android:scheme="recaptcha" />
167            </intent-filter>
168        </activity>
169
170        <provider
170-->[:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
171            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
171-->[:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
172            android:authorities="com.ukilgiri.app.flutter.image_provider"
172-->[:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
173            android:exported="false"
173-->[:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
174            android:grantUriPermissions="true" >
174-->[:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
175            <meta-data
175-->[:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
176                android:name="android.support.FILE_PROVIDER_PATHS"
176-->[:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
177                android:resource="@xml/flutter_image_picker_file_paths" />
177-->[:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
178        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
179        <service
179-->[:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
180            android:name="com.google.android.gms.metadata.ModuleDependencies"
180-->[:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
181            android:enabled="false"
181-->[:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
182            android:exported="false" >
182-->[:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
183            <intent-filter>
183-->[:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
184                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
184-->[:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
184-->[:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
185            </intent-filter>
186
187            <meta-data
187-->[:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
188                android:name="photopicker_activity:0:required"
188-->[:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
189                android:value="" />
189-->[:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
190        </service>
191
192        <activity
192-->[:url_launcher_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
193            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
193-->[:url_launcher_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
194            android:exported="false"
194-->[:url_launcher_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
195            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
195-->[:url_launcher_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
196
197        <uses-library
197-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3392fa676a925dd7c068329e3015472\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
198            android:name="androidx.window.extensions"
198-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3392fa676a925dd7c068329e3015472\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
199            android:required="false" />
199-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3392fa676a925dd7c068329e3015472\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
200        <uses-library
200-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3392fa676a925dd7c068329e3015472\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
201            android:name="androidx.window.sidecar"
201-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3392fa676a925dd7c068329e3015472\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
202            android:required="false" />
202-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3392fa676a925dd7c068329e3015472\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
203
204        <activity
204-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4371d7a4116c7ece602da711ecb35109\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
205            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
205-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4371d7a4116c7ece602da711ecb35109\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
206            android:excludeFromRecents="true"
206-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4371d7a4116c7ece602da711ecb35109\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
207            android:exported="false"
207-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4371d7a4116c7ece602da711ecb35109\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
208            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
208-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4371d7a4116c7ece602da711ecb35109\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
209        <!--
210            Service handling Google Sign-In user revocation. For apps that do not integrate with
211            Google Sign-In, this service will never be started.
212        -->
213        <service
213-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4371d7a4116c7ece602da711ecb35109\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
214            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
214-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4371d7a4116c7ece602da711ecb35109\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
215            android:exported="true"
215-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4371d7a4116c7ece602da711ecb35109\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
216            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
216-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4371d7a4116c7ece602da711ecb35109\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
217            android:visibleToInstantApps="true" />
217-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4371d7a4116c7ece602da711ecb35109\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
218
219        <provider
219-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\870c4a4d8ab6921fb09c369d676fe19c\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:23:9-28:39
220            android:name="com.google.firebase.provider.FirebaseInitProvider"
220-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\870c4a4d8ab6921fb09c369d676fe19c\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:24:13-77
221            android:authorities="com.ukilgiri.app.firebaseinitprovider"
221-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\870c4a4d8ab6921fb09c369d676fe19c\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:25:13-72
222            android:directBootAware="true"
222-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\870c4a4d8ab6921fb09c369d676fe19c\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:26:13-43
223            android:exported="false"
223-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\870c4a4d8ab6921fb09c369d676fe19c\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:27:13-37
224            android:initOrder="100" />
224-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\870c4a4d8ab6921fb09c369d676fe19c\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:28:13-36
225        <provider
225-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8392d77bfd9488b1f3382d53b788af15\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
226            android:name="androidx.startup.InitializationProvider"
226-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8392d77bfd9488b1f3382d53b788af15\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
227            android:authorities="com.ukilgiri.app.androidx-startup"
227-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8392d77bfd9488b1f3382d53b788af15\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
228            android:exported="false" >
228-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8392d77bfd9488b1f3382d53b788af15\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
229            <meta-data
229-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8392d77bfd9488b1f3382d53b788af15\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
230                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
230-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8392d77bfd9488b1f3382d53b788af15\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
231                android:value="androidx.startup" />
231-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8392d77bfd9488b1f3382d53b788af15\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
232            <meta-data
232-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
233                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
233-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
234                android:value="androidx.startup" />
234-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
235        </provider>
236
237        <activity
237-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f9271a16790b10f6f400891616470b9a\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
238            android:name="com.google.android.gms.common.api.GoogleApiActivity"
238-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f9271a16790b10f6f400891616470b9a\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
239            android:exported="false"
239-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f9271a16790b10f6f400891616470b9a\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
240            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
240-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f9271a16790b10f6f400891616470b9a\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
241
242        <meta-data
242-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\058a70fc25a41fe25172013c6f3c7c95\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
243            android:name="com.google.android.gms.version"
243-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\058a70fc25a41fe25172013c6f3c7c95\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
244            android:value="@integer/google_play_services_version" />
244-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\058a70fc25a41fe25172013c6f3c7c95\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
245
246        <receiver
246-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
247            android:name="androidx.profileinstaller.ProfileInstallReceiver"
247-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
248            android:directBootAware="false"
248-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
249            android:enabled="true"
249-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
250            android:exported="true"
250-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
251            android:permission="android.permission.DUMP" >
251-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
252            <intent-filter>
252-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
253                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
253-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
253-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
254            </intent-filter>
255            <intent-filter>
255-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
256                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
256-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
256-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
257            </intent-filter>
258            <intent-filter>
258-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
259                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
259-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
259-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
260            </intent-filter>
261            <intent-filter>
261-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
262                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
262-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
262-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
263            </intent-filter>
264        </receiver>
265    </application>
266
267</manifest>
