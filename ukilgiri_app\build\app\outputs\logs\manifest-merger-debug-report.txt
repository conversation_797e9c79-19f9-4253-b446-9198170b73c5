-- Merging decision tree log ---
application
INJECTED from C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:10:5-44:19
INJECTED from C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\debug\AndroidManifest.xml
MERGED from [:firebase_auth] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_auth] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:cloud_firestore] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:cloud_firestore] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_storage] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_storage] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_core] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_core] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\d83f2e8efb23072280f9a46b6129fa9a\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\d83f2e8efb23072280f9a46b6129fa9a\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6528413aa607479f9d4a8f42981c48b7\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:26:5-37:19
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6528413aa607479f9d4a8f42981c48b7\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:26:5-37:19
MERGED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\ef83a5b1da0a826e04415baff613d9b5\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:21:5-32:19
MERGED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\ef83a5b1da0a826e04415baff613d9b5\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:21:5-32:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\b32d05bfe0ef284c76253bd0a5e650d5\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\b32d05bfe0ef284c76253bd0a5e650d5\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:8:5-16:19
MERGED from [:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-32:19
MERGED from [:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-32:19
MERGED from [:url_launcher_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-12:19
MERGED from [:url_launcher_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-12:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3392fa676a925dd7c068329e3015472\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3392fa676a925dd7c068329e3015472\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4371d7a4116c7ece602da711ecb35109\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4371d7a4116c7ece602da711ecb35109\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bb22cd4d45fa05c250a53d7b2ec8fb89\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bb22cd4d45fa05c250a53d7b2ec8fb89\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\870c4a4d8ab6921fb09c369d676fe19c\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\870c4a4d8ab6921fb09c369d676fe19c\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\6630dd9d03ceb1f13f8263f2d2d07a26\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\6630dd9d03ceb1f13f8263f2d2d07a26\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\588733bc69d81da4f608d7ebc8d2e720\transformed\jetified-integrity-1.2.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\588733bc69d81da4f608d7ebc8d2e720\transformed\jetified-integrity-1.2.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\36c8633a4164b7d385ab10969ab08a1d\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:21:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\36c8633a4164b7d385ab10969ab08a1d\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:21:5-20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8392d77bfd9488b1f3382d53b788af15\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8392d77bfd9488b1f3382d53b788af15\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f9271a16790b10f6f400891616470b9a\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f9271a16790b10f6f400891616470b9a\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\cdf62c17d3ddc06dcebbe84695d2e2aa\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\cdf62c17d3ddc06dcebbe84695d2e2aa\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\058a70fc25a41fe25172013c6f3c7c95\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\058a70fc25a41fe25172013c6f3c7c95\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e7e3c83388001da39a5d44ececd8e9e\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e7e3c83388001da39a5d44ececd8e9e\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\8a2d9558fbb56f4071cb46b712479c80\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\8a2d9558fbb56f4071cb46b712479c80\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\683574a22736d48c9476e9229e27d553\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\683574a22736d48c9476e9229e27d553\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e7e3c83388001da39a5d44ececd8e9e\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:name
		INJECTED from C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml
manifest
ADDED from C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:1:1-56:12
MERGED from C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:1:1-56:12
INJECTED from C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\debug\AndroidManifest.xml:1:1-7:12
MERGED from [:shared_preferences_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\shared_preferences_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:firebase_auth] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:cloud_firestore] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:firebase_storage] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:firebase_core] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:17:1-75:12
MERGED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\d83f2e8efb23072280f9a46b6129fa9a\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:2:1-26:12
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6528413aa607479f9d4a8f42981c48b7\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:15:1-39:12
MERGED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\ef83a5b1da0a826e04415baff613d9b5\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:15:1-34:12
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\b32d05bfe0ef284c76253bd0a5e650d5\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:2:1-18:12
MERGED from [:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-34:12
MERGED from [:url_launcher_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-14:12
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:google_sign_in_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\google_sign_in_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [:permission_handler_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\permission_handler_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\0aec81ac1045261b57de152fa0160835\transformed\preference-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\57ca061d6f83c02836e249974e533058\transformed\browser-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b289f56be5257f2324d34e69e30cc692\transformed\recyclerview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d430e2d17c6b57593cbf8f133de1437f\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f957f910da7d903b6101965e45f78431\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3392fa676a925dd7c068329e3015472\transformed\jetified-window-1.2.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d761947bd5978c96dc7d9087dcea15f7\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:17:1-21:12
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4371d7a4116c7ece602da711ecb35109\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:17:1-40:12
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\584a7d9af8fd0860fd9671dd1719eb16\transformed\appcompat-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bb22cd4d45fa05c250a53d7b2ec8fb89\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\870c4a4d8ab6921fb09c369d676fe19c\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\de7b1822f03da4238b7e67db3f2d9c3d\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\transforms-3\a2b7789360f1d5d838cd1d0ed29137b6\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\6630dd9d03ceb1f13f8263f2d2d07a26\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\f05cbc1323a19b3d7510097d6e3c9a69\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\588733bc69d81da4f608d7ebc8d2e720\transformed\jetified-integrity-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\36c8633a4164b7d385ab10969ab08a1d\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:15:1-23:12
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\d2e0c1d2d699a83a2f91cc307fd5ec47\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\aeb5b15299fe464bd07903965dcc04c1\transformed\jetified-activity-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e02478319b0ce858b690d0837de1709\transformed\jetified-activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\5ad80befc30cc722d880eecf57a2ff65\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\6d7eadf4c04564f9de5b7929043d02aa\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\15caf625181f688b605d0b0f582fc7ee\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d2af524fff83f2676a1167277869fa8e\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\4987cd2e1beea703829338af6b4e2de8\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0fd56fbbc92a4aa22ad7e0e5df8c3dc5\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a1cfa91b80fdc366f8f9b7b1b771cf2\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8392d77bfd9488b1f3382d53b788af15\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a57b6fed72e30b8ad662c51f04032e4e\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\046ba1c904796709f73eecd5feb628f4\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\337d71935036f47f9bb4d838cbdb1d2f\transformed\jetified-datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\f9b8fe91cfd6df3f7bbaad9f41d057e0\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\216417ecc90f95cf5d6737c726deb0ac\transformed\jetified-datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\16cc37cb47795d9125b88d530386e5fd\transformed\jetified-firebase-database-collection-18.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f9271a16790b10f6f400891616470b9a\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\cdf62c17d3ddc06dcebbe84695d2e2aa\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\058a70fc25a41fe25172013c6f3c7c95\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\641ab33f69bb12a33b5460068aad9382\transformed\fragment-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f0bdc6f84797ed68e88cccd0b7dfda91\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8155b8973e932a3e84c6730fe96a2922\transformed\loader-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\6839cd751f1f71447f65cc549b661e36\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\58bc622968d5949c1107bd62cfea08b8\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d93be0c4f4c9012a809c661248324a0\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\aba7bca1a2d8143b57e55919e0df6552\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3a56d197344080e419b793ccc3ea6c80\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\57489ec3af251e461a16d9c39f8248a6\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\f07f343c356e342fc6826481ac1cc2e4\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\6618e579f1fe3ce9c7f7307585aaf5d3\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\0784d41da40c7d5f69d736c95efbc7d5\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e9e0a9e2a454eea15f31b1d9c884decf\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\26a76581ad76ba0c972c7558801305b5\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e7e3c83388001da39a5d44ececd8e9e\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\transforms-3\e3ae8030d7ffe47771d58f2822832354\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\8a2d9558fbb56f4071cb46b712479c80\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d09e693c49c1569f91c69f3ae320d8f1\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4eb3b3f887e512b526b9e3f14ad9d2aa\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\683574a22736d48c9476e9229e27d553\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5add1e6ac3daa0314aab37981d8d2311\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\transforms-3\882a9ad7cdbb93198f8d79c46f2d3ef4\transformed\jetified-firebase-components-17.1.5\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\54da33a6c28bad795c178532eb01fcbf\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7548e20a4411a629851bcb0821b75703\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e357cb7fb15ebff6cd128c526b18c33\transformed\jetified-core-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e0e3a8789ea8c5c7a8e928d8895ada5c\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2b967f887caa4001dc89a598e9f8fa7\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\87db32e2439fce478b8839255c2f0246\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f7d69835676e60d0ecc95cdcaf025c90\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\transforms-3\0d2d8b1ab1ce0f6ec6712ade850419d1\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:2:1-7:12
MERGED from [io.grpc:grpc-android:1.57.2] C:\Users\<USER>\.gradle\caches\transforms-3\8191c46f5170cdc86910ec14ae687124\transformed\jetified-grpc-android-1.57.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b106294e06248aa70a60aa2bce52794a\transformed\jetified-protolite-well-known-types-18.0.0\AndroidManifest.xml:2:1-11:12
	package
		INJECTED from C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\debug\AndroidManifest.xml
	android:versionCode
		INJECTED from C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:3:5-67
MERGED from C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:3:5-67
MERGED from C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:3:5-67
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\d83f2e8efb23072280f9a46b6129fa9a\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:11:5-67
MERGED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\d83f2e8efb23072280f9a46b6129fa9a\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:11:5-67
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6528413aa607479f9d4a8f42981c48b7\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6528413aa607479f9d4a8f42981c48b7\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:24:5-67
MERGED from [:google_sign_in_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\google_sign_in_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:google_sign_in_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\google_sign_in_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\f05cbc1323a19b3d7510097d6e3c9a69\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\f05cbc1323a19b3d7510097d6e3c9a69\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:7:5-67
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:3:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:4:5-79
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\d83f2e8efb23072280f9a46b6129fa9a\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:10:5-79
MERGED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\d83f2e8efb23072280f9a46b6129fa9a\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:10:5-79
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6528413aa607479f9d4a8f42981c48b7\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6528413aa607479f9d4a8f42981c48b7\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\f05cbc1323a19b3d7510097d6e3c9a69\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\f05cbc1323a19b3d7510097d6e3c9a69\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:8:5-79
MERGED from [io.grpc:grpc-android:1.57.2] C:\Users\<USER>\.gradle\caches\transforms-3\8191c46f5170cdc86910ec14ae687124\transformed\jetified-grpc-android-1.57.2\AndroidManifest.xml:7:5-79
MERGED from [io.grpc:grpc-android:1.57.2] C:\Users\<USER>\.gradle\caches\transforms-3\8191c46f5170cdc86910ec14ae687124\transformed\jetified-grpc-android-1.57.2\AndroidManifest.xml:7:5-79
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:4:22-76
uses-permission#android.permission.CAMERA
ADDED from C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:5:5-65
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:5:22-62
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:6:5-80
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:6:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:7:5-81
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:7:22-78
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:8:5-76
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:8:22-73
queries
ADDED from C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:50:5-55:15
intent#action:name:android.intent.action.PROCESS_TEXT+data:mimeType:text/plain
ADDED from C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:51:9-54:18
action#android.intent.action.PROCESS_TEXT
ADDED from C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:52:13-72
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:52:21-70
data
ADDED from C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:53:13-50
	android:mimeType
		ADDED from C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:53:19-48
uses-sdk
INJECTED from C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\debug\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\debug\AndroidManifest.xml
MERGED from [:shared_preferences_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\shared_preferences_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\shared_preferences_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_auth] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_auth] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:cloud_firestore] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:cloud_firestore] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_storage] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_storage] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:21:5-23:64
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:21:5-23:64
MERGED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\d83f2e8efb23072280f9a46b6129fa9a\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\d83f2e8efb23072280f9a46b6129fa9a\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6528413aa607479f9d4a8f42981c48b7\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6528413aa607479f9d4a8f42981c48b7\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\ef83a5b1da0a826e04415baff613d9b5\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\ef83a5b1da0a826e04415baff613d9b5\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\b32d05bfe0ef284c76253bd0a5e650d5\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\b32d05bfe0ef284c76253bd0a5e650d5\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:5:5-44
MERGED from [:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:url_launcher_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:google_sign_in_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\google_sign_in_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:google_sign_in_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\google_sign_in_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\permission_handler_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\permission_handler_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\0aec81ac1045261b57de152fa0160835\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\0aec81ac1045261b57de152fa0160835\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\57ca061d6f83c02836e249974e533058\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\57ca061d6f83c02836e249974e533058\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b289f56be5257f2324d34e69e30cc692\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b289f56be5257f2324d34e69e30cc692\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d430e2d17c6b57593cbf8f133de1437f\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d430e2d17c6b57593cbf8f133de1437f\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f957f910da7d903b6101965e45f78431\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f957f910da7d903b6101965e45f78431\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3392fa676a925dd7c068329e3015472\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3392fa676a925dd7c068329e3015472\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d761947bd5978c96dc7d9087dcea15f7\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d761947bd5978c96dc7d9087dcea15f7\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4371d7a4116c7ece602da711ecb35109\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4371d7a4116c7ece602da711ecb35109\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\584a7d9af8fd0860fd9671dd1719eb16\transformed\appcompat-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\584a7d9af8fd0860fd9671dd1719eb16\transformed\appcompat-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bb22cd4d45fa05c250a53d7b2ec8fb89\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bb22cd4d45fa05c250a53d7b2ec8fb89\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\870c4a4d8ab6921fb09c369d676fe19c\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\870c4a4d8ab6921fb09c369d676fe19c\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:19:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\de7b1822f03da4238b7e67db3f2d9c3d\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\de7b1822f03da4238b7e67db3f2d9c3d\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\transforms-3\a2b7789360f1d5d838cd1d0ed29137b6\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\transforms-3\a2b7789360f1d5d838cd1d0ed29137b6\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\6630dd9d03ceb1f13f8263f2d2d07a26\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\6630dd9d03ceb1f13f8263f2d2d07a26\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\f05cbc1323a19b3d7510097d6e3c9a69\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\f05cbc1323a19b3d7510097d6e3c9a69\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\588733bc69d81da4f608d7ebc8d2e720\transformed\jetified-integrity-1.2.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\588733bc69d81da4f608d7ebc8d2e720\transformed\jetified-integrity-1.2.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\36c8633a4164b7d385ab10969ab08a1d\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\36c8633a4164b7d385ab10969ab08a1d\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\d2e0c1d2d699a83a2f91cc307fd5ec47\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\d2e0c1d2d699a83a2f91cc307fd5ec47\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\aeb5b15299fe464bd07903965dcc04c1\transformed\jetified-activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\aeb5b15299fe464bd07903965dcc04c1\transformed\jetified-activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e02478319b0ce858b690d0837de1709\transformed\jetified-activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e02478319b0ce858b690d0837de1709\transformed\jetified-activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\5ad80befc30cc722d880eecf57a2ff65\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\5ad80befc30cc722d880eecf57a2ff65\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\6d7eadf4c04564f9de5b7929043d02aa\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\6d7eadf4c04564f9de5b7929043d02aa\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\15caf625181f688b605d0b0f582fc7ee\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\15caf625181f688b605d0b0f582fc7ee\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d2af524fff83f2676a1167277869fa8e\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d2af524fff83f2676a1167277869fa8e\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\4987cd2e1beea703829338af6b4e2de8\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\4987cd2e1beea703829338af6b4e2de8\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0fd56fbbc92a4aa22ad7e0e5df8c3dc5\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0fd56fbbc92a4aa22ad7e0e5df8c3dc5\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a1cfa91b80fdc366f8f9b7b1b771cf2\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a1cfa91b80fdc366f8f9b7b1b771cf2\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8392d77bfd9488b1f3382d53b788af15\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8392d77bfd9488b1f3382d53b788af15\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a57b6fed72e30b8ad662c51f04032e4e\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a57b6fed72e30b8ad662c51f04032e4e\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\046ba1c904796709f73eecd5feb628f4\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\046ba1c904796709f73eecd5feb628f4\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\337d71935036f47f9bb4d838cbdb1d2f\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\337d71935036f47f9bb4d838cbdb1d2f\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\f9b8fe91cfd6df3f7bbaad9f41d057e0\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\f9b8fe91cfd6df3f7bbaad9f41d057e0\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\216417ecc90f95cf5d6737c726deb0ac\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\216417ecc90f95cf5d6737c726deb0ac\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\16cc37cb47795d9125b88d530386e5fd\transformed\jetified-firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\16cc37cb47795d9125b88d530386e5fd\transformed\jetified-firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f9271a16790b10f6f400891616470b9a\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f9271a16790b10f6f400891616470b9a\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\cdf62c17d3ddc06dcebbe84695d2e2aa\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\cdf62c17d3ddc06dcebbe84695d2e2aa\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\058a70fc25a41fe25172013c6f3c7c95\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\058a70fc25a41fe25172013c6f3c7c95\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\641ab33f69bb12a33b5460068aad9382\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\641ab33f69bb12a33b5460068aad9382\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f0bdc6f84797ed68e88cccd0b7dfda91\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f0bdc6f84797ed68e88cccd0b7dfda91\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8155b8973e932a3e84c6730fe96a2922\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8155b8973e932a3e84c6730fe96a2922\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\6839cd751f1f71447f65cc549b661e36\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\6839cd751f1f71447f65cc549b661e36\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\58bc622968d5949c1107bd62cfea08b8\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\58bc622968d5949c1107bd62cfea08b8\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d93be0c4f4c9012a809c661248324a0\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d93be0c4f4c9012a809c661248324a0\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\aba7bca1a2d8143b57e55919e0df6552\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\aba7bca1a2d8143b57e55919e0df6552\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3a56d197344080e419b793ccc3ea6c80\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3a56d197344080e419b793ccc3ea6c80\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\57489ec3af251e461a16d9c39f8248a6\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\57489ec3af251e461a16d9c39f8248a6\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\f07f343c356e342fc6826481ac1cc2e4\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\f07f343c356e342fc6826481ac1cc2e4\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\6618e579f1fe3ce9c7f7307585aaf5d3\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\6618e579f1fe3ce9c7f7307585aaf5d3\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\0784d41da40c7d5f69d736c95efbc7d5\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\0784d41da40c7d5f69d736c95efbc7d5\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e9e0a9e2a454eea15f31b1d9c884decf\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e9e0a9e2a454eea15f31b1d9c884decf\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\26a76581ad76ba0c972c7558801305b5\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\26a76581ad76ba0c972c7558801305b5\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e7e3c83388001da39a5d44ececd8e9e\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e7e3c83388001da39a5d44ececd8e9e\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\transforms-3\e3ae8030d7ffe47771d58f2822832354\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\transforms-3\e3ae8030d7ffe47771d58f2822832354\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\8a2d9558fbb56f4071cb46b712479c80\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\8a2d9558fbb56f4071cb46b712479c80\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d09e693c49c1569f91c69f3ae320d8f1\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d09e693c49c1569f91c69f3ae320d8f1\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4eb3b3f887e512b526b9e3f14ad9d2aa\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4eb3b3f887e512b526b9e3f14ad9d2aa\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\683574a22736d48c9476e9229e27d553\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\683574a22736d48c9476e9229e27d553\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5add1e6ac3daa0314aab37981d8d2311\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5add1e6ac3daa0314aab37981d8d2311\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\transforms-3\882a9ad7cdbb93198f8d79c46f2d3ef4\transformed\jetified-firebase-components-17.1.5\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\transforms-3\882a9ad7cdbb93198f8d79c46f2d3ef4\transformed\jetified-firebase-components-17.1.5\AndroidManifest.xml:18:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\54da33a6c28bad795c178532eb01fcbf\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\54da33a6c28bad795c178532eb01fcbf\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7548e20a4411a629851bcb0821b75703\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7548e20a4411a629851bcb0821b75703\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e357cb7fb15ebff6cd128c526b18c33\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e357cb7fb15ebff6cd128c526b18c33\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e0e3a8789ea8c5c7a8e928d8895ada5c\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e0e3a8789ea8c5c7a8e928d8895ada5c\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2b967f887caa4001dc89a598e9f8fa7\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2b967f887caa4001dc89a598e9f8fa7\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\87db32e2439fce478b8839255c2f0246\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\87db32e2439fce478b8839255c2f0246\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f7d69835676e60d0ecc95cdcaf025c90\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f7d69835676e60d0ecc95cdcaf025c90\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\transforms-3\0d2d8b1ab1ce0f6ec6712ade850419d1\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\transforms-3\0d2d8b1ab1ce0f6ec6712ade850419d1\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [io.grpc:grpc-android:1.57.2] C:\Users\<USER>\.gradle\caches\transforms-3\8191c46f5170cdc86910ec14ae687124\transformed\jetified-grpc-android-1.57.2\AndroidManifest.xml:5:5-44
MERGED from [io.grpc:grpc-android:1.57.2] C:\Users\<USER>\.gradle\caches\transforms-3\8191c46f5170cdc86910ec14ae687124\transformed\jetified-grpc-android-1.57.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b106294e06248aa70a60aa2bce52794a\transformed\jetified-protolite-well-known-types-18.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b106294e06248aa70a60aa2bce52794a\transformed\jetified-protolite-well-known-types-18.0.0\AndroidManifest.xml:7:5-9:41
	tools:overrideLibrary
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:23:9-61
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\debug\AndroidManifest.xml
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [:firebase_auth] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:cloud_firestore] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:cloud_firestore] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_storage] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_storage] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_core] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_core] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\d83f2e8efb23072280f9a46b6129fa9a\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\d83f2e8efb23072280f9a46b6129fa9a\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6528413aa607479f9d4a8f42981c48b7\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:27:9-36:19
MERGED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6528413aa607479f9d4a8f42981c48b7\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:27:9-36:19
MERGED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\ef83a5b1da0a826e04415baff613d9b5\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:22:9-31:19
MERGED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\ef83a5b1da0a826e04415baff613d9b5\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:22:9-31:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\b32d05bfe0ef284c76253bd0a5e650d5\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\b32d05bfe0ef284c76253bd0a5e650d5\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\870c4a4d8ab6921fb09c369d676fe19c\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\870c4a4d8ab6921fb09c369d676fe19c\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:30:9-38:19
	android:exported
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:68:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\870c4a4d8ab6921fb09c369d676fe19c\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\870c4a4d8ab6921fb09c369d676fe19c\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [:firebase_auth] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-89
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar
ADDED from [:firebase_auth] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:firebase_auth] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:firebase_auth] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.firestore.FlutterFirebaseFirestoreRegistrar
ADDED from [:cloud_firestore] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:cloud_firestore] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:cloud_firestore] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-134
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.storage.FlutterFirebaseAppRegistrar
ADDED from [:firebase_storage] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:firebase_storage] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:firebase_storage] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-126
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar
ADDED from [:firebase_core] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:firebase_core] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:firebase_core] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
activity#com.google.firebase.auth.internal.GenericIdpActivity
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:29:9-46:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:31:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:33:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:32:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:34:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:30:13-80
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:genericidp
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:35:13-45:29
action#android.intent.action.VIEW
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:17-69
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:25-66
category#android.intent.category.DEFAULT
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:17-76
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:27-73
category#android.intent.category.BROWSABLE
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:17-78
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:27-75
activity#com.google.firebase.auth.internal.RecaptchaActivity
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:47:9-64:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:49:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:51:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:50:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:52:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:48:13-79
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:recaptcha
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:53:13-63:29
meta-data#com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:69:13-71:85
	android:value
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:71:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:70:17-109
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar
ADDED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\d83f2e8efb23072280f9a46b6129fa9a\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:17:13-19:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\d83f2e8efb23072280f9a46b6129fa9a\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:19:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\d83f2e8efb23072280f9a46b6129fa9a\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:18:17-122
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar
ADDED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\d83f2e8efb23072280f9a46b6129fa9a\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\d83f2e8efb23072280f9a46b6129fa9a\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\d83f2e8efb23072280f9a46b6129fa9a\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:21:17-111
meta-data#com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar
ADDED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6528413aa607479f9d4a8f42981c48b7\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:30:13-32:85
	android:value
		ADDED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6528413aa607479f9d4a8f42981c48b7\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:32:17-82
	android:name
		ADDED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6528413aa607479f9d4a8f42981c48b7\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:31:17-118
meta-data#com.google.firebase.components:com.google.firebase.storage.StorageRegistrar
ADDED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6528413aa607479f9d4a8f42981c48b7\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:33:13-35:85
	android:value
		ADDED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6528413aa607479f9d4a8f42981c48b7\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:35:17-82
	android:name
		ADDED from [com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6528413aa607479f9d4a8f42981c48b7\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:34:17-107
meta-data#com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar
ADDED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\ef83a5b1da0a826e04415baff613d9b5\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\ef83a5b1da0a826e04415baff613d9b5\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\ef83a5b1da0a826e04415baff613d9b5\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:26:17-120
meta-data#com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar
ADDED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\ef83a5b1da0a826e04415baff613d9b5\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:28:13-30:85
	android:value
		ADDED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\ef83a5b1da0a826e04415baff613d9b5\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:30:17-82
	android:name
		ADDED from [com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\ef83a5b1da0a826e04415baff613d9b5\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:29:17-117
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\b32d05bfe0ef284c76253bd0a5e650d5\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\b32d05bfe0ef284c76253bd0a5e650d5\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\b32d05bfe0ef284c76253bd0a5e650d5\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:13:17-116
provider#io.flutter.plugins.imagepicker.ImagePickerFileProvider
ADDED from [:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
	android:grantUriPermissions
		ADDED from [:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
	android:authorities
		ADDED from [:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
	android:exported
		ADDED from [:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
	android:resource
		ADDED from [:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
service#com.google.android.gms.metadata.ModuleDependencies
ADDED from [:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
	android:enabled
		ADDED from [:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
	android:exported
		ADDED from [:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
	tools:ignore
		ADDED from [:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-40
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
intent-filter#action:name:com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
action#com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
meta-data#photopicker_activity:0:required
ADDED from [:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
	android:value
		ADDED from [:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
activity#io.flutter.plugins.urllauncher.WebViewActivity
ADDED from [:url_launcher_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
	android:exported
		ADDED from [:url_launcher_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [:url_launcher_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
	android:name
		ADDED from [:url_launcher_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3392fa676a925dd7c068329e3015472\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3392fa676a925dd7c068329e3015472\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3392fa676a925dd7c068329e3015472\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3392fa676a925dd7c068329e3015472\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3392fa676a925dd7c068329e3015472\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3392fa676a925dd7c068329e3015472\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4371d7a4116c7ece602da711ecb35109\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4371d7a4116c7ece602da711ecb35109\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4371d7a4116c7ece602da711ecb35109\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4371d7a4116c7ece602da711ecb35109\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4371d7a4116c7ece602da711ecb35109\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4371d7a4116c7ece602da711ecb35109\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4371d7a4116c7ece602da711ecb35109\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4371d7a4116c7ece602da711ecb35109\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4371d7a4116c7ece602da711ecb35109\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4371d7a4116c7ece602da711ecb35109\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\870c4a4d8ab6921fb09c369d676fe19c\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\870c4a4d8ab6921fb09c369d676fe19c\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\870c4a4d8ab6921fb09c369d676fe19c\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\870c4a4d8ab6921fb09c369d676fe19c\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\870c4a4d8ab6921fb09c369d676fe19c\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\870c4a4d8ab6921fb09c369d676fe19c\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\870c4a4d8ab6921fb09c369d676fe19c\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\870c4a4d8ab6921fb09c369d676fe19c\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\870c4a4d8ab6921fb09c369d676fe19c\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:36:17-109
uses-permission#com.google.android.providers.gsf.permission.READ_GSERVICES
ADDED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\f05cbc1323a19b3d7510097d6e3c9a69\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:9:5-98
	android:name
		ADDED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\f05cbc1323a19b3d7510097d6e3c9a69\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:9:22-95
provider#androidx.startup.InitializationProvider
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8392d77bfd9488b1f3382d53b788af15\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\8a2d9558fbb56f4071cb46b712479c80\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\8a2d9558fbb56f4071cb46b712479c80\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8392d77bfd9488b1f3382d53b788af15\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8392d77bfd9488b1f3382d53b788af15\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8392d77bfd9488b1f3382d53b788af15\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8392d77bfd9488b1f3382d53b788af15\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8392d77bfd9488b1f3382d53b788af15\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8392d77bfd9488b1f3382d53b788af15\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8392d77bfd9488b1f3382d53b788af15\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f9271a16790b10f6f400891616470b9a\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f9271a16790b10f6f400891616470b9a\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f9271a16790b10f6f400891616470b9a\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f9271a16790b10f6f400891616470b9a\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\058a70fc25a41fe25172013c6f3c7c95\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\058a70fc25a41fe25172013c6f3c7c95\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\058a70fc25a41fe25172013c6f3c7c95\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e7e3c83388001da39a5d44ececd8e9e\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e7e3c83388001da39a5d44ececd8e9e\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e7e3c83388001da39a5d44ececd8e9e\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.ukilgiri.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e7e3c83388001da39a5d44ececd8e9e\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e7e3c83388001da39a5d44ececd8e9e\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e7e3c83388001da39a5d44ececd8e9e\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e7e3c83388001da39a5d44ececd8e9e\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e7e3c83388001da39a5d44ececd8e9e\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.ukilgiri.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e7e3c83388001da39a5d44ececd8e9e\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e7e3c83388001da39a5d44ececd8e9e\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
