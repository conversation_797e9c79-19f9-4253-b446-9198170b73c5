["C:\\Users\\<USER>\\Documents\\augment-projects\\new\\ukilgiri_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\assets\\images\\README.md", "C:\\Users\\<USER>\\Documents\\augment-projects\\new\\ukilgiri_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\assets\\images\\salauddin.jpg", "C:\\Users\\<USER>\\Documents\\augment-projects\\new\\ukilgiri_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\assets\\images\\salauddin.jpg.placeholder", "C:\\Users\\<USER>\\Documents\\augment-projects\\new\\ukilgiri_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\assets\\images\\surovi.jpg", "C:\\Users\\<USER>\\Documents\\augment-projects\\new\\ukilgiri_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\assets\\images\\surovi.jpg.placeholder", "C:\\Users\\<USER>\\Documents\\augment-projects\\new\\ukilgiri_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\assets\\fonts\\HindSiliguri-Regular.ttf", "C:\\Users\\<USER>\\Documents\\augment-projects\\new\\ukilgiri_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\assets\\fonts\\HindSiliguri-Medium.ttf", "C:\\Users\\<USER>\\Documents\\augment-projects\\new\\ukilgiri_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\assets\\fonts\\HindSiliguri-SemiBold.ttf", "C:\\Users\\<USER>\\Documents\\augment-projects\\new\\ukilgiri_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\assets\\fonts\\HindSiliguri-Bold.ttf", "C:\\Users\\<USER>\\Documents\\augment-projects\\new\\ukilgiri_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\packages\\cupertino_icons\\assets\\CupertinoIcons.ttf", "C:\\Users\\<USER>\\Documents\\augment-projects\\new\\ukilgiri_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\fonts\\MaterialIcons-Regular.otf", "C:\\Users\\<USER>\\Documents\\augment-projects\\new\\ukilgiri_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\shaders\\ink_sparkle.frag", "C:\\Users\\<USER>\\Documents\\augment-projects\\new\\ukilgiri_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\AssetManifest.json", "C:\\Users\\<USER>\\Documents\\augment-projects\\new\\ukilgiri_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\AssetManifest.bin", "C:\\Users\\<USER>\\Documents\\augment-projects\\new\\ukilgiri_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\FontManifest.json", "C:\\Users\\<USER>\\Documents\\augment-projects\\new\\ukilgiri_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\NOTICES.Z", "C:\\Users\\<USER>\\Documents\\augment-projects\\new\\ukilgiri_app\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\NativeAssetsManifest.json", "C:\\Users\\<USER>\\Documents\\augment-projects\\new\\ukilgiri_app\\build\\app\\intermediates\\flutter\\release\\armeabi-v7a\\app.so", "C:\\Users\\<USER>\\Documents\\augment-projects\\new\\ukilgiri_app\\build\\app\\intermediates\\flutter\\release\\x86_64\\app.so", "C:\\Users\\<USER>\\Documents\\augment-projects\\new\\ukilgiri_app\\build\\app\\intermediates\\flutter\\release\\arm64-v8a\\app.so"]