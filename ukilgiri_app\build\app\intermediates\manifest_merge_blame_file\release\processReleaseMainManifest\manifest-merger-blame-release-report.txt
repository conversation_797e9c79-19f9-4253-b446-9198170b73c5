1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.ukilgiri_app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!-- Internet permission for API calls -->
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:3:5-67
11-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:3:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:4:5-79
12-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:4:22-76
13    <uses-permission android:name="android.permission.CAMERA" />
13-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:5:5-65
13-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:5:22-62
14    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
14-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:6:5-80
14-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:6:22-77
15    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
15-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:7:5-81
15-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:7:22-78
16    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
16-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:8:5-76
16-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:8:22-73
17    <!--
18         Required to query activities that can process text, see:
19         https://developer.android.com/training/package-visibility?hl=en and
20         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
21
22         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
23    -->
24    <queries>
24-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:50:5-55:15
25        <intent>
25-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:51:9-54:18
26            <action android:name="android.intent.action.PROCESS_TEXT" />
26-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:52:13-72
26-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:52:21-70
27
28            <data android:mimeType="text/plain" />
28-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:53:13-50
28-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:53:19-48
29        </intent>
30    </queries>
31
32    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
32-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\f05cbc1323a19b3d7510097d6e3c9a69\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:9:5-98
32-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\f05cbc1323a19b3d7510097d6e3c9a69\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:9:22-95
33
34    <permission
34-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e7e3c83388001da39a5d44ececd8e9e\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
35        android:name="com.example.ukilgiri_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
35-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e7e3c83388001da39a5d44ececd8e9e\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
36        android:protectionLevel="signature" />
36-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e7e3c83388001da39a5d44ececd8e9e\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
37
38    <uses-permission android:name="com.example.ukilgiri_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
38-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e7e3c83388001da39a5d44ececd8e9e\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
38-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e7e3c83388001da39a5d44ececd8e9e\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
39
40    <application
41        android:name="android.app.Application"
41-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:12:9-42
42        android:allowBackup="true"
42-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:14:9-35
43        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
43-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e7e3c83388001da39a5d44ececd8e9e\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
44        android:extractNativeLibs="true"
45        android:icon="@mipmap/ic_launcher"
45-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:13:9-43
46        android:label="UkilGiri - উকিলগিরি"
46-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:11:9-44
47        android:requestLegacyExternalStorage="true"
47-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:17:9-52
48        android:supportsRtl="true"
48-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:15:9-35
49        android:usesCleartextTraffic="true" >
49-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:16:9-44
50        <activity
50-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:18:9-38:20
51            android:name="com.example.ukilgiri_app.MainActivity"
51-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:19:13-41
52            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
52-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:23:13-163
53            android:exported="true"
53-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:20:13-36
54            android:hardwareAccelerated="true"
54-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:24:13-47
55            android:launchMode="singleTop"
55-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:21:13-43
56            android:theme="@style/LaunchTheme"
56-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:22:13-47
57            android:windowSoftInputMode="adjustResize" >
57-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:25:13-55
58
59            <!--
60                 Specifies an Android theme to apply to this Activity as soon as
61                 the Android process has started. This theme is visible to the user
62                 while the Flutter UI initializes. After that, this theme continues
63                 to determine the Window background behind the Flutter UI.
64            -->
65            <meta-data
65-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:30:13-33:17
66                android:name="io.flutter.embedding.android.NormalTheme"
66-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:31:15-70
67                android:resource="@style/NormalTheme" />
67-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:32:15-52
68
69            <intent-filter>
69-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:34:13-37:29
70                <action android:name="android.intent.action.MAIN" />
70-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:35:17-68
70-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:35:25-66
71
72                <category android:name="android.intent.category.LAUNCHER" />
72-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:36:17-76
72-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:36:27-74
73            </intent-filter>
74        </activity>
75        <!--
76             Don't delete the meta-data below.
77             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
78        -->
79        <meta-data
79-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:41:9-43:33
80            android:name="flutterEmbedding"
80-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:42:13-44
81            android:value="2" />
81-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:43:13-30
82
83        <service
83-->[:firebase_auth] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_auth\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-12:19
84            android:name="com.google.firebase.components.ComponentDiscoveryService"
84-->[:firebase_auth] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_auth\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:18-89
85            android:directBootAware="true"
85-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\870c4a4d8ab6921fb09c369d676fe19c\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:32:13-43
86            android:exported="false" >
86-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:68:13-37
87            <meta-data
87-->[:firebase_auth] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_auth\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-11:85
88                android:name="com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar"
88-->[:firebase_auth] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_auth\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:17-124
89                android:value="com.google.firebase.components.ComponentRegistrar" />
89-->[:firebase_auth] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_auth\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:17-82
90            <meta-data
90-->[:cloud_firestore] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\cloud_firestore\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-11:85
91                android:name="com.google.firebase.components:io.flutter.plugins.firebase.firestore.FlutterFirebaseFirestoreRegistrar"
91-->[:cloud_firestore] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\cloud_firestore\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:17-134
92                android:value="com.google.firebase.components.ComponentRegistrar" />
92-->[:cloud_firestore] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\cloud_firestore\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:17-82
93            <meta-data
93-->[:firebase_storage] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_storage\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-11:85
94                android:name="com.google.firebase.components:io.flutter.plugins.firebase.storage.FlutterFirebaseAppRegistrar"
94-->[:firebase_storage] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_storage\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:17-126
95                android:value="com.google.firebase.components.ComponentRegistrar" />
95-->[:firebase_storage] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_storage\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:17-82
96            <meta-data
96-->[:firebase_core] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_core\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-11:85
97                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
97-->[:firebase_core] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_core\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:17-124
98                android:value="com.google.firebase.components.ComponentRegistrar" />
98-->[:firebase_core] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\firebase_core\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:17-82
99            <meta-data
99-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:69:13-71:85
100                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
100-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:70:17-109
101                android:value="com.google.firebase.components.ComponentRegistrar" />
101-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:71:17-82
102            <meta-data
102-->[com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\d83f2e8efb23072280f9a46b6129fa9a\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:17:13-19:85
103                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
103-->[com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\d83f2e8efb23072280f9a46b6129fa9a\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:18:17-122
104                android:value="com.google.firebase.components.ComponentRegistrar" />
104-->[com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\d83f2e8efb23072280f9a46b6129fa9a\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:19:17-82
105            <meta-data
105-->[com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\d83f2e8efb23072280f9a46b6129fa9a\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:20:13-22:85
106                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
106-->[com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\d83f2e8efb23072280f9a46b6129fa9a\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:21:17-111
107                android:value="com.google.firebase.components.ComponentRegistrar" />
107-->[com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\d83f2e8efb23072280f9a46b6129fa9a\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:22:17-82
108            <meta-data
108-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6528413aa607479f9d4a8f42981c48b7\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:30:13-32:85
109                android:name="com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar"
109-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6528413aa607479f9d4a8f42981c48b7\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:31:17-118
110                android:value="com.google.firebase.components.ComponentRegistrar" />
110-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6528413aa607479f9d4a8f42981c48b7\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:32:17-82
111            <meta-data
111-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6528413aa607479f9d4a8f42981c48b7\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:33:13-35:85
112                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
112-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6528413aa607479f9d4a8f42981c48b7\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:34:17-107
113                android:value="com.google.firebase.components.ComponentRegistrar" />
113-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6528413aa607479f9d4a8f42981c48b7\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:35:17-82
114            <meta-data
114-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\ef83a5b1da0a826e04415baff613d9b5\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:25:13-27:85
115                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
115-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\ef83a5b1da0a826e04415baff613d9b5\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:26:17-120
116                android:value="com.google.firebase.components.ComponentRegistrar" />
116-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\ef83a5b1da0a826e04415baff613d9b5\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:27:17-82
117            <meta-data
117-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\ef83a5b1da0a826e04415baff613d9b5\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:28:13-30:85
118                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
118-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\ef83a5b1da0a826e04415baff613d9b5\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:29:17-117
119                android:value="com.google.firebase.components.ComponentRegistrar" />
119-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\ef83a5b1da0a826e04415baff613d9b5\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:30:17-82
120            <meta-data
120-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\b32d05bfe0ef284c76253bd0a5e650d5\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:12:13-14:85
121                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
121-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\b32d05bfe0ef284c76253bd0a5e650d5\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:13:17-116
122                android:value="com.google.firebase.components.ComponentRegistrar" />
122-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\b32d05bfe0ef284c76253bd0a5e650d5\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:14:17-82
123            <meta-data
123-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\870c4a4d8ab6921fb09c369d676fe19c\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:35:13-37:85
124                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
124-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\870c4a4d8ab6921fb09c369d676fe19c\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:36:17-109
125                android:value="com.google.firebase.components.ComponentRegistrar" />
125-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\870c4a4d8ab6921fb09c369d676fe19c\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:37:17-82
126        </service>
127
128        <activity
128-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:29:9-46:20
129            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
129-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:30:13-80
130            android:excludeFromRecents="true"
130-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:31:13-46
131            android:exported="true"
131-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:32:13-36
132            android:launchMode="singleTask"
132-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:33:13-44
133            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
133-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:34:13-72
134            <intent-filter>
134-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:35:13-45:29
135                <action android:name="android.intent.action.VIEW" />
135-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:17-69
135-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:25-66
136
137                <category android:name="android.intent.category.DEFAULT" />
137-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:17-76
137-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:27-73
138                <category android:name="android.intent.category.BROWSABLE" />
138-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:17-78
138-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:27-75
139
140                <data
140-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:53:13-50
141                    android:host="firebase.auth"
142                    android:path="/"
143                    android:scheme="genericidp" />
144            </intent-filter>
145        </activity>
146        <activity
146-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:47:9-64:20
147            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
147-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:48:13-79
148            android:excludeFromRecents="true"
148-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:49:13-46
149            android:exported="true"
149-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:50:13-36
150            android:launchMode="singleTask"
150-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:51:13-44
151            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
151-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:52:13-72
152            <intent-filter>
152-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:53:13-63:29
153                <action android:name="android.intent.action.VIEW" />
153-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:17-69
153-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:25-66
154
155                <category android:name="android.intent.category.DEFAULT" />
155-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:17-76
155-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:27-73
156                <category android:name="android.intent.category.BROWSABLE" />
156-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:17-78
156-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5cb739d125e07dcc58ecd9aafa4d342\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:27-75
157
158                <data
158-->C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\android\app\src\main\AndroidManifest.xml:53:13-50
159                    android:host="firebase.auth"
160                    android:path="/"
161                    android:scheme="recaptcha" />
162            </intent-filter>
163        </activity>
164
165        <provider
165-->[:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:9-17:20
166            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
166-->[:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-82
167            android:authorities="com.example.ukilgiri_app.flutter.image_provider"
167-->[:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-74
168            android:exported="false"
168-->[:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-37
169            android:grantUriPermissions="true" >
169-->[:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-47
170            <meta-data
170-->[:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-16:75
171                android:name="android.support.FILE_PROVIDER_PATHS"
171-->[:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-67
172                android:resource="@xml/flutter_image_picker_file_paths" />
172-->[:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:17-72
173        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
174        <service
174-->[:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:9-31:19
175            android:name="com.google.android.gms.metadata.ModuleDependencies"
175-->[:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-78
176            android:enabled="false"
176-->[:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-36
177            android:exported="false" >
177-->[:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-37
178            <intent-filter>
178-->[:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:13-26:29
179                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
179-->[:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:17-94
179-->[:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:25-91
180            </intent-filter>
181
182            <meta-data
182-->[:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:13-30:36
183                android:name="photopicker_activity:0:required"
183-->[:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:17-63
184                android:value="" />
184-->[:image_picker_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:17-33
185        </service>
186
187        <activity
187-->[:url_launcher_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-11:74
188            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
188-->[:url_launcher_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-74
189            android:exported="false"
189-->[:url_launcher_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-37
190            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
190-->[:url_launcher_android] C:\Users\<USER>\Documents\augment-projects\new\ukilgiri_app\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-71
191
192        <uses-library
192-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3392fa676a925dd7c068329e3015472\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
193            android:name="androidx.window.extensions"
193-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3392fa676a925dd7c068329e3015472\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
194            android:required="false" />
194-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3392fa676a925dd7c068329e3015472\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
195        <uses-library
195-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3392fa676a925dd7c068329e3015472\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
196            android:name="androidx.window.sidecar"
196-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3392fa676a925dd7c068329e3015472\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
197            android:required="false" />
197-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3392fa676a925dd7c068329e3015472\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
198
199        <activity
199-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4371d7a4116c7ece602da711ecb35109\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
200            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
200-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4371d7a4116c7ece602da711ecb35109\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
201            android:excludeFromRecents="true"
201-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4371d7a4116c7ece602da711ecb35109\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
202            android:exported="false"
202-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4371d7a4116c7ece602da711ecb35109\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
203            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
203-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4371d7a4116c7ece602da711ecb35109\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
204        <!--
205            Service handling Google Sign-In user revocation. For apps that do not integrate with
206            Google Sign-In, this service will never be started.
207        -->
208        <service
208-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4371d7a4116c7ece602da711ecb35109\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
209            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
209-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4371d7a4116c7ece602da711ecb35109\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
210            android:exported="true"
210-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4371d7a4116c7ece602da711ecb35109\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
211            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
211-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4371d7a4116c7ece602da711ecb35109\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
212            android:visibleToInstantApps="true" />
212-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4371d7a4116c7ece602da711ecb35109\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
213
214        <provider
214-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\870c4a4d8ab6921fb09c369d676fe19c\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:23:9-28:39
215            android:name="com.google.firebase.provider.FirebaseInitProvider"
215-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\870c4a4d8ab6921fb09c369d676fe19c\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:24:13-77
216            android:authorities="com.example.ukilgiri_app.firebaseinitprovider"
216-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\870c4a4d8ab6921fb09c369d676fe19c\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:25:13-72
217            android:directBootAware="true"
217-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\870c4a4d8ab6921fb09c369d676fe19c\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:26:13-43
218            android:exported="false"
218-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\870c4a4d8ab6921fb09c369d676fe19c\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:27:13-37
219            android:initOrder="100" />
219-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\870c4a4d8ab6921fb09c369d676fe19c\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:28:13-36
220        <provider
220-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8392d77bfd9488b1f3382d53b788af15\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
221            android:name="androidx.startup.InitializationProvider"
221-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8392d77bfd9488b1f3382d53b788af15\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
222            android:authorities="com.example.ukilgiri_app.androidx-startup"
222-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8392d77bfd9488b1f3382d53b788af15\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
223            android:exported="false" >
223-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8392d77bfd9488b1f3382d53b788af15\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
224            <meta-data
224-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8392d77bfd9488b1f3382d53b788af15\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
225                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
225-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8392d77bfd9488b1f3382d53b788af15\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
226                android:value="androidx.startup" />
226-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8392d77bfd9488b1f3382d53b788af15\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
227            <meta-data
227-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
228                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
228-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
229                android:value="androidx.startup" />
229-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
230        </provider>
231
232        <activity
232-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f9271a16790b10f6f400891616470b9a\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
233            android:name="com.google.android.gms.common.api.GoogleApiActivity"
233-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f9271a16790b10f6f400891616470b9a\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
234            android:exported="false"
234-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f9271a16790b10f6f400891616470b9a\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
235            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
235-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f9271a16790b10f6f400891616470b9a\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
236
237        <meta-data
237-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\058a70fc25a41fe25172013c6f3c7c95\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
238            android:name="com.google.android.gms.version"
238-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\058a70fc25a41fe25172013c6f3c7c95\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
239            android:value="@integer/google_play_services_version" />
239-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\058a70fc25a41fe25172013c6f3c7c95\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
240
241        <receiver
241-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
242            android:name="androidx.profileinstaller.ProfileInstallReceiver"
242-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
243            android:directBootAware="false"
243-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
244            android:enabled="true"
244-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
245            android:exported="true"
245-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
246            android:permission="android.permission.DUMP" >
246-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
247            <intent-filter>
247-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
248                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
248-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
248-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
249            </intent-filter>
250            <intent-filter>
250-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
251                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
251-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
251-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
252            </intent-filter>
253            <intent-filter>
253-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
254                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
254-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
254-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
255            </intent-filter>
256            <intent-filter>
256-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
257                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
257-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
257-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
258            </intent-filter>
259        </receiver>
260    </application>
261
262</manifest>
